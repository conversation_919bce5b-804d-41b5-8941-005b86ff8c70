"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./src/components/FAQSection.tsx":
/*!***************************************!*\
  !*** ./src/components/FAQSection.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FAQSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=HelpCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _lib_messages__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/messages */ \"(app-pages-browser)/./src/lib/messages.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n// FAQ Section with chatbot integration for enhanced user experience\n\n\nfunction FAQSection(param) {\n    let { locale } = param;\n    const messages = (0,_lib_messages__WEBPACK_IMPORTED_MODULE_1__.getMessagesSync)(locale);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center space-x-2 bg-primary-100 text-primary-700 rounded-full px-4 py-2 text-sm font-semibold\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: messages.faq.getInstantHelp\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl lg:text-4xl font-heading font-bold text-neutral-800 leading-tight\",\n                            children: messages.faq.needHelpChatWithUs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-neutral-600 max-w-2xl mx-auto\",\n                            children: messages.faq.chatbotAvailable24_7\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-neutral-800 mb-3\",\n                                children: messages.faq.useOurChatbot\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-neutral-600 mb-4 max-w-xl mx-auto\",\n                                children: messages.faq.lookForChatIcon\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/80 rounded-xl p-3 border-l-4 border-primary-500\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-neutral-700 text-sm\",\n                                    children: [\n                                        messages.faq.askAboutVehicles,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 48\n                                        }, this),\n                                        messages.faq.englishJapaneseSupport,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 54\n                                        }, this),\n                                        messages.faq.instantResponses\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\website\\\\ebammotors\\\\src\\\\components\\\\FAQSection.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n_c = FAQSection;\nvar _c;\n$RefreshReg$(_c, \"FAQSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FAQSection.tsx\n"));

/***/ })

});